import { INTERNAL_MIME_TYPES } from "@dust-tt/client";
import type { Notification } from "@modelcontextprotocol/sdk/types.js";
import { NotificationSchema } from "@modelcontextprotocol/sdk/types.js";
import { z } from "zod";

import type { SupportedFileContentType } from "@app/types";
import { FILE_FORMATS } from "@app/types";

// Redeclared here to avoid an issue with the zod types in the @modelcontextprotocol/sdk
// See https://github.com/colinhacks/zod/issues/2938
const ResourceContentsSchema = z.object({
  uri: z.string(),
  mimeType: z.optional(z.string()),
});

const TextResourceContentsSchema = ResourceContentsSchema.extend({
  text: z.string(),
});

const BlobResourceContentsSchema = ResourceContentsSchema.extend({
  blob: z.string().base64(),
});

const TextContentSchema = z.object({
  type: z.literal("text"),
  text: z.string(),
});

const ImageContentSchema = z.object({
  type: z.literal("image"),
  data: z.string().base64(),
  mimeType: z.string(),
});

// File generated by the tool during its execution.

const ToolGeneratedFileSchema = z.object({
  text: z.string(),
  uri: z.string(),
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.FILE),
  fileId: z.string(),
  title: z.string(),
  contentType: z.enum(
    Object.keys(FILE_FORMATS) as [
      SupportedFileContentType,
      ...SupportedFileContentType[],
    ]
  ),
  snippet: z.string().nullable(),
});

export type ToolGeneratedFileType = z.infer<typeof ToolGeneratedFileSchema>;

export function isToolGeneratedFile(
  outputBlock: MCPToolResultContentType
): outputBlock is { type: "resource"; resource: ToolGeneratedFileType } {
  return (
    outputBlock.type === "resource" &&
    ToolGeneratedFileSchema.safeParse(outputBlock.resource).success
  );
}

// Thinking tokens generated during the tool execution.

const ThinkingOutputSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.THINKING),
  text: z.string(),
  uri: z.literal(""),
});

export type ThinkingOutputType = z.infer<typeof ThinkingOutputSchema>;

export function isThinkingOutput(
  outputBlock: MCPToolResultContentType
): outputBlock is { type: "resource"; resource: ThinkingOutputType } {
  return (
    outputBlock.type === "resource" &&
    ThinkingOutputSchema.safeParse(outputBlock.resource).success
  );
}

// Final output of the reasoning when successful with the non-CoT tokens.

export const ReasoningSuccessOutputSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.REASONING_SUCCESS),
  text: z.string(),
  uri: z.literal(""),
});

export type ReasoningSuccessOutputType = z.infer<
  typeof ReasoningSuccessOutputSchema
>;

export function isReasoningSuccessOutput(
  outputBlock: MCPToolResultContentType
): outputBlock is { type: "resource"; resource: ReasoningSuccessOutputType } {
  return (
    outputBlock.type === "resource" &&
    ReasoningSuccessOutputSchema.safeParse(outputBlock.resource).success
  );
}

// SQL query generated during the tool execution.

const SqlQueryOutputSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.SQL_QUERY),
  text: z.string(),
  uri: z.literal(""),
});

export type SqlQueryOutputType = z.infer<typeof SqlQueryOutputSchema>;

export function isSqlQueryOutput(
  outputBlock: MCPToolResultContentType
): outputBlock is { type: "resource"; resource: SqlQueryOutputType } {
  return (
    outputBlock.type === "resource" &&
    SqlQueryOutputSchema.safeParse(outputBlock.resource).success
  );
}

// Resource with a name.

type ResourceWithName = {
  name: string;
};

export const isResourceWithName = (
  resource: object
): resource is ResourceWithName => {
  return "name" in resource && typeof resource.name === "string";
};

export const DatabaseSchemaResourceSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.DATABASE_SCHEMA),
  text: z.string(),
  uri: z.string(),
});

export type DatabaseSchemaResourceType = z.infer<
  typeof DatabaseSchemaResourceSchema
>;

export const isDatabaseSchemaResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is {
  type: "resource";
  resource: DatabaseSchemaResourceType;
} => {
  return (
    outputBlock.type === "resource" &&
    DatabaseSchemaResourceSchema.safeParse(outputBlock.resource).success
  );
};

export const QueryWritingInstructionsResourceSchema = z.object({
  mimeType: z.literal(
    INTERNAL_MIME_TYPES.TOOL_OUTPUT.QUERY_WRITING_INSTRUCTIONS
  ),
  text: z.string(),
  uri: z.string(),
});

export type QueryWritingInstructionsResourceType = z.infer<
  typeof QueryWritingInstructionsResourceSchema
>;

export const isQueryWritingInstructionsResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is {
  type: "resource";
  resource: QueryWritingInstructionsResourceType;
} => {
  return (
    outputBlock.type === "resource" &&
    QueryWritingInstructionsResourceSchema.safeParse(outputBlock.resource)
      .success
  );
};

export const ExampleRowsResourceSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.EXAMPLE_ROWS),
  text: z.string(),
  uri: z.string(),
});

export type ExampleRowsResourceType = z.infer<typeof ExampleRowsResourceSchema>;

export const isExampleRowsResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is { type: "resource"; resource: ExampleRowsResourceType } => {
  return (
    outputBlock.type === "resource" &&
    ExampleRowsResourceSchema.safeParse(outputBlock.resource).success
  );
};

// Data source search outputs: query and results.

export const SearchQueryResourceSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.DATA_SOURCE_SEARCH_QUERY),
  text: z.string(),
  uri: z.literal(""),
});

export type SearchQueryResourceType = z.infer<typeof SearchQueryResourceSchema>;

export const isSearchQueryResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is { type: "resource"; resource: SearchQueryResourceType } => {
  return (
    outputBlock.type === "resource" &&
    SearchQueryResourceSchema.safeParse(outputBlock.resource).success
  );
};

export const SearchResultResourceSchema = z.object({
  mimeType: z.literal(
    INTERNAL_MIME_TYPES.TOOL_OUTPUT.DATA_SOURCE_SEARCH_RESULT
  ),
  uri: z.string(),
  text: z.string(),

  // Document metadata
  id: z.string(),
  tags: z.array(z.string()),
  ref: z.string(),
  chunks: z.array(z.string()),
  source: z.object({
    name: z.string(),
    provider: z.string().optional(),
  }),
});

export type SearchResultResourceType = z.infer<
  typeof SearchResultResourceSchema
>;

export const isSearchResultResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is { type: "resource"; resource: SearchResultResourceType } => {
  return (
    outputBlock.type === "resource" &&
    SearchResultResourceSchema.safeParse(outputBlock.resource).success
  );
};

// Data source inclusion outputs, query and results
export const IncludeQueryResourceSchema = z.object({
  mimeType: z.literal(
    INTERNAL_MIME_TYPES.TOOL_OUTPUT.DATA_SOURCE_INCLUDE_QUERY
  ),
  text: z.string(),
  warning: z
    .object({
      title: z.string(),
      description: z.string(),
    })
    .optional(),
  uri: z.literal(""),
});

export type IncludeQueryResourceType = z.infer<
  typeof IncludeQueryResourceSchema
>;

export const isIncludeQueryResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is { type: "resource"; resource: IncludeQueryResourceType } => {
  return (
    outputBlock.type === "resource" &&
    IncludeQueryResourceSchema.safeParse(outputBlock.resource).success
  );
};

export const IncludeResultResourceSchema = z.object({
  mimeType: z.literal(
    INTERNAL_MIME_TYPES.TOOL_OUTPUT.DATA_SOURCE_INCLUDE_RESULT
  ),
  uri: z.string(),
  text: z.string(),

  // Document metadata
  id: z.string(),
  tags: z.array(z.string()),
  ref: z.string(),
  chunks: z.array(z.string()),
  source: z.object({
    name: z.string(),
    provider: z.string().optional(),
  }),
});

export type IncludeResultResourceType = z.infer<
  typeof IncludeResultResourceSchema
>;

export const isIncludeResultResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is { type: "resource"; resource: IncludeResultResourceType } => {
  return (
    outputBlock.type === "resource" &&
    IncludeResultResourceSchema.safeParse(outputBlock.resource).success
  );
};

// Websearch results.

export const WebsearchQueryResourceSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.WEBSEARCH_QUERY),
  text: z.string(),
  uri: z.literal(""),
});

export type WebsearchQueryResourceType = z.infer<
  typeof WebsearchQueryResourceSchema
>;

export const isWebsearchQueryResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is {
  type: "resource";
  resource: WebsearchQueryResourceType;
} => {
  return (
    outputBlock.type === "resource" &&
    WebsearchQueryResourceSchema.safeParse(outputBlock.resource).success
  );
};

export const WebsearchResultResourceSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.WEBSEARCH_RESULT),
  title: z.string(),
  text: z.string(),
  uri: z.string(),
  reference: z.string(),
});

export type WebsearchResultResourceType = z.infer<
  typeof WebsearchResultResourceSchema
>;

export const isWebsearchResultResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is {
  type: "resource";
  resource: WebsearchResultResourceType;
} => {
  return (
    outputBlock.type === "resource" &&
    WebsearchResultResourceSchema.safeParse(outputBlock.resource).success
  );
};

// Browse results.

export const BrowseResultResourceSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.BROWSE_RESULT),
  requestedUrl: z.string(),
  uri: z.string(), // Browsed url, might differ from the requested url
  text: z.string(),
  title: z.string().optional(),
  description: z.string().optional(),
  responseCode: z.string(),
  errorMessage: z.string().optional(),
});

export type BrowseResultResourceType = z.infer<
  typeof BrowseResultResourceSchema
>;

export const isBrowseResultResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is {
  type: "resource";
  resource: BrowseResultResourceType;
} => {
  return (
    outputBlock.type === "resource" &&
    BrowseResultResourceSchema.safeParse(outputBlock.resource).success
  );
};

// RunAgent results.

export const RunAgentQueryResourceSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.RUN_AGENT_QUERY),
  text: z.string(),
  childAgentId: z.string(),
  uri: z.literal(""),
});

export type RunAgentQueryResourceType = z.infer<
  typeof RunAgentQueryResourceSchema
>;

export const isRunAgentQueryResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is {
  type: "resource";
  resource: RunAgentQueryResourceType;
} => {
  return (
    outputBlock.type === "resource" &&
    RunAgentQueryResourceSchema.safeParse(outputBlock.resource).success
  );
};

export const RunAgentResultResourceSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.RUN_AGENT_RESULT),
  conversationId: z.string(),
  text: z.string(),
  uri: z.string(),
});

export type RunAgentResultResourceType = z.infer<
  typeof RunAgentResultResourceSchema
>;

export const isRunAgentResultResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is {
  type: "resource";
  resource: RunAgentResultResourceType;
} => {
  return (
    outputBlock.type === "resource" &&
    RunAgentResultResourceSchema.safeParse(outputBlock.resource).success
  );
};

// Personal authentication required error.

export const PersonalAuthenticationRequiredErrorResourceSchema = z.object({
  mimeType: z.literal(
    INTERNAL_MIME_TYPES.TOOL_ERROR.PERSONAL_AUTHENTICATION_REQUIRED
  ),
  text: z.string(),
  uri: z.literal(""),
  mcpServerId: z.string(),
  provider: z.string(),
  useCase: z.string(),
  scope: z.string().optional(),
});

export type PersonalAuthenticationRequiredErrorResourceType = z.infer<
  typeof PersonalAuthenticationRequiredErrorResourceSchema
>;

// Extract data outputs: query and results.

export const ExtractQueryResourceSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.EXTRACT_QUERY),
  text: z.string(),
  uri: z.literal(""),
});

export type ExtractQueryResourceType = z.infer<
  typeof ExtractQueryResourceSchema
>;

export const isExtractQueryResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is { type: "resource"; resource: ExtractQueryResourceType } => {
  return (
    outputBlock.type === "resource" &&
    ExtractQueryResourceSchema.safeParse(outputBlock.resource).success
  );
};

export const ExtractResultResourceSchema = z.object({
  mimeType: z.literal(INTERNAL_MIME_TYPES.TOOL_OUTPUT.EXTRACT_RESULT),
  uri: z.string(),
  text: z.string(),

  // File metadata
  fileId: z.string(),
  title: z.string(),
  contentType: z.string(),
  snippet: z.string().nullable(),
});

export type ExtractResultResourceType = z.infer<
  typeof ExtractResultResourceSchema
>;

export const isExtractResultResourceType = (
  outputBlock: MCPToolResultContentType
): outputBlock is { type: "resource"; resource: ExtractResultResourceType } => {
  return (
    outputBlock.type === "resource" &&
    ExtractResultResourceSchema.safeParse(outputBlock.resource).success
  );
};

// Generic output types and schemas.

const EmbeddedResourceSchema = z.object({
  type: z.literal("resource"),
  resource: z.union([
    BlobResourceContentsSchema,
    DatabaseSchemaResourceSchema,
    QueryWritingInstructionsResourceSchema,
    ExampleRowsResourceSchema,
    SearchQueryResourceSchema,
    SearchResultResourceSchema,
    ExtractQueryResourceSchema,
    ExtractResultResourceSchema,
    TextResourceContentsSchema,
    ThinkingOutputSchema,
    ToolGeneratedFileSchema,
    RunAgentQueryResourceSchema,
    RunAgentResultResourceSchema,
    PersonalAuthenticationRequiredErrorResourceSchema,
  ]),
});

const MCPToolResultContentSchema = z.union([
  TextContentSchema,
  ImageContentSchema,
  EmbeddedResourceSchema,
]);

export type MCPToolResultContentType = z.infer<
  typeof MCPToolResultContentSchema
>;

export type MCPToolResult = {
  isError: boolean;
  content: MCPToolResultContentType[];
};

/**
 * Notification output types.
 */

const NotificationImageContentSchema = z.object({
  type: z.literal("image"),
  mimeType: z.string(),
});

type ImageProgressOutput = z.infer<typeof NotificationImageContentSchema>;

export const ProgressNotificationOutputSchema = z
  .union([NotificationImageContentSchema, TextContentSchema])
  .optional();

type ProgressNotificationOutput = z.infer<
  typeof ProgressNotificationOutputSchema
>;

export function isImageProgressOutput(
  output: ProgressNotificationOutput
): output is ImageProgressOutput {
  return output !== undefined && output.type === "image";
}

export const ProgressNotificationContentSchema = z.object({
  // Required for the MCP protocol.
  progress: z.number(),
  total: z.number(),
  progressToken: z.union([z.string(), z.number()]),
  // Custom data.
  data: z.object({
    label: z.string(),
    output: ProgressNotificationOutputSchema,
  }),
});

export type ProgressNotificationContentType = z.infer<
  typeof ProgressNotificationContentSchema
>;

export const MCPProgressNotificationSchema = NotificationSchema.extend({
  method: z.literal("notifications/progress"),
  params: ProgressNotificationContentSchema,
});

export type MCPProgressNotificationType = z.infer<
  typeof MCPProgressNotificationSchema
>;

export function isMCPProgressNotificationType(
  notification: Notification
): notification is MCPProgressNotificationType {
  return MCPProgressNotificationSchema.safeParse(notification).success;
}
