import { INTERNAL_MIME_TYPES } from "@dust-tt/client";
import { describe, expect, it } from "vitest";

import { getMCPServerRequirements } from "./input_configuration";
import { ConfigurableToolInputJSONSchemas } from "./input_schemas";
import type { MCPServerViewType } from "@app/lib/api/mcp";

// Helper function to create a mock MCPServerViewType
function createMockServerView(tools: any[]): MCPServerViewType {
  return {
    id: 1,
    sId: "test-server",
    createdAt: Date.now(),
    updatedAt: Date.now(),
    spaceId: "space-1",
    serverType: "internal",
    editedByUser: null,
    server: {
      sId: "test-server",
      name: "test-server",
      version: "1.0.0",
      description: "Test server",
      icon: "ActionEmotionLaughIcon",
      authorization: null,
      availability: "manual",
      tools,
    },
  };
}

describe("getMCPServerRequirements - End-to-End Tests", () => {
  it("should return default values when mcpServerView is null or undefined", () => {
    const expectedDefaults = {
      requiresDataSourceConfiguration: false,
      requiresTableConfiguration: false,
      requiresChildAgentConfiguration: false,
      requiresReasoningConfiguration: false,
      requiresDustAppConfiguration: false,
      requiredStrings: [],
      requiredNumbers: [],
      requiredBooleans: [],
      requiredEnums: {},
      mayRequireTimeFrameConfiguration: false,
      mayRequireJsonSchemaConfiguration: false,
      noRequirement: false,
    };

    expect(getMCPServerRequirements(null)).toEqual(expectedDefaults);
    expect(getMCPServerRequirements(undefined)).toEqual(expectedDefaults);
  });

  it("should detect data source requirements", () => {
    const serverView = createMockServerView([
      {
        name: "search_data_sources",
        description: "Search through data sources",
        inputSchema: {
          type: "object",
          properties: {
            query: { type: "string" },
            dataSources: ConfigurableToolInputJSONSchemas[INTERNAL_MIME_TYPES.TOOL_INPUT.DATA_SOURCE],
          },
        },
      },
    ]);

    const result = getMCPServerRequirements(serverView);
    
    expect(result.requiresDataSourceConfiguration).toBe(true);
    expect(result.noRequirement).toBe(false);
  });

  it("should detect table requirements", () => {
    const serverView = createMockServerView([
      {
        name: "query_tables",
        description: "Query database tables",
        inputSchema: {
          type: "object",
          properties: {
            query: { type: "string" },
            tables: ConfigurableToolInputJSONSchemas[INTERNAL_MIME_TYPES.TOOL_INPUT.TABLE],
          },
        },
      },
    ]);

    const result = getMCPServerRequirements(serverView);
    
    expect(result.requiresTableConfiguration).toBe(true);
    expect(result.noRequirement).toBe(false);
  });

  it("should detect agent requirements", () => {
    const serverView = createMockServerView([
      {
        name: "run_agent",
        description: "Run a child agent",
        inputSchema: {
          type: "object",
          properties: {
            instructions: { type: "string" },
            agent: ConfigurableToolInputJSONSchemas[INTERNAL_MIME_TYPES.TOOL_INPUT.AGENT],
          },
        },
      },
    ]);

    const result = getMCPServerRequirements(serverView);
    
    expect(result.requiresChildAgentConfiguration).toBe(true);
    expect(result.noRequirement).toBe(false);
  });

  it("should detect reasoning model requirements", () => {
    const serverView = createMockServerView([
      {
        name: "reasoning_task",
        description: "Perform reasoning with a specific model",
        inputSchema: {
          type: "object",
          properties: {
            task: { type: "string" },
            reasoningModel: ConfigurableToolInputJSONSchemas[INTERNAL_MIME_TYPES.TOOL_INPUT.REASONING_MODEL],
          },
        },
      },
    ]);

    const result = getMCPServerRequirements(serverView);
    
    expect(result.requiresReasoningConfiguration).toBe(true);
    expect(result.noRequirement).toBe(false);
  });

  it("should detect dust app requirements", () => {
    const serverView = createMockServerView([
      {
        name: "run_dust_app",
        description: "Run a Dust application",
        inputSchema: {
          type: "object",
          properties: {
            input: { type: "string" },
            dustApp: ConfigurableToolInputJSONSchemas[INTERNAL_MIME_TYPES.TOOL_INPUT.DUST_APP],
          },
        },
      },
    ]);

    const result = getMCPServerRequirements(serverView);
    
    expect(result.requiresDustAppConfiguration).toBe(true);
    expect(result.noRequirement).toBe(false);
  });

  it("should detect string requirements", () => {
    const serverView = createMockServerView([
      {
        name: "configure_strings",
        description: "Tool with string configurations",
        inputSchema: {
          type: "object",
          properties: {
            apiKey: ConfigurableToolInputJSONSchemas[INTERNAL_MIME_TYPES.TOOL_INPUT.STRING],
            endpoint: ConfigurableToolInputJSONSchemas[INTERNAL_MIME_TYPES.TOOL_INPUT.STRING],
            nested: {
              type: "object",
              properties: {
                secretKey: ConfigurableToolInputJSONSchemas[INTERNAL_MIME_TYPES.TOOL_INPUT.STRING],
              },
            },
          },
        },
      },
    ]);

    const result = getMCPServerRequirements(serverView);
    
    expect(result.requiredStrings).toContain("apiKey");
    expect(result.requiredStrings).toContain("endpoint");
    expect(result.requiredStrings).toContain("nested.secretKey");
    expect(result.noRequirement).toBe(false);
  });

  it("should detect number requirements", () => {
    const serverView = createMockServerView([
      {
        name: "configure_numbers",
        description: "Tool with number configurations",
        inputSchema: {
          type: "object",
          properties: {
            timeout: ConfigurableToolInputJSONSchemas[INTERNAL_MIME_TYPES.TOOL_INPUT.NUMBER],
            maxRetries: ConfigurableToolInputJSONSchemas[INTERNAL_MIME_TYPES.TOOL_INPUT.NUMBER],
          },
        },
      },
    ]);

    const result = getMCPServerRequirements(serverView);
    
    expect(result.requiredNumbers).toContain("timeout");
    expect(result.requiredNumbers).toContain("maxRetries");
    expect(result.noRequirement).toBe(false);
  });

  it("should detect boolean requirements", () => {
    const serverView = createMockServerView([
      {
        name: "configure_booleans",
        description: "Tool with boolean configurations",
        inputSchema: {
          type: "object",
          properties: {
            enableLogging: ConfigurableToolInputJSONSchemas[INTERNAL_MIME_TYPES.TOOL_INPUT.BOOLEAN],
            strictMode: ConfigurableToolInputJSONSchemas[INTERNAL_MIME_TYPES.TOOL_INPUT.BOOLEAN],
          },
        },
      },
    ]);

    const result = getMCPServerRequirements(serverView);
    
    expect(result.requiredBooleans).toContain("enableLogging");
    expect(result.requiredBooleans).toContain("strictMode");
    expect(result.noRequirement).toBe(false);
  });

  it("should detect enum requirements", () => {
    const serverView = createMockServerView([
      {
        name: "configure_enums",
        description: "Tool with enum configurations",
        inputSchema: {
          type: "object",
          properties: {
            logLevel: {
              type: "object",
              properties: {
                value: {
                  type: "string",
                  enum: ["debug", "info", "warn", "error"],
                },
                mimeType: { const: INTERNAL_MIME_TYPES.TOOL_INPUT.ENUM },
              },
            },
            environment: {
              type: "object",
              properties: {
                value: {
                  type: "string",
                  enum: ["development", "staging", "production"],
                },
                mimeType: { const: INTERNAL_MIME_TYPES.TOOL_INPUT.ENUM },
              },
            },
          },
        },
      },
    ]);

    const result = getMCPServerRequirements(serverView);
    
    expect(result.requiredEnums).toHaveProperty("logLevel");
    expect(result.requiredEnums.logLevel).toEqual(["debug", "info", "warn", "error"]);
    expect(result.requiredEnums).toHaveProperty("environment");
    expect(result.requiredEnums.environment).toEqual(["development", "staging", "production"]);
    expect(result.noRequirement).toBe(false);
  });

  it("should detect timeFrame requirements", () => {
    const serverView = createMockServerView([
      {
        name: "time_based_search",
        description: "Search with time constraints",
        inputSchema: {
          type: "object",
          properties: {
            query: { type: "string" },
            timeFrame: {
              type: "object",
              properties: {
                duration: { type: "number" },
                unit: { type: "string", enum: ["hour", "day", "week", "month", "year"] },
              },
            },
          },
        },
      },
    ]);

    const result = getMCPServerRequirements(serverView);
    
    expect(result.mayRequireTimeFrameConfiguration).toBe(true);
    expect(result.noRequirement).toBe(false);
  });

  it("should detect jsonSchema requirements", () => {
    const serverView = createMockServerView([
      {
        name: "extract_data",
        description: "Extract structured data",
        inputSchema: {
          type: "object",
          properties: {
            input: { type: "string" },
            jsonSchema: {
              type: "object",
              properties: {
                type: { type: "string" },
                properties: { type: "object" },
              },
            },
          },
        },
      },
    ]);

    const result = getMCPServerRequirements(serverView);
    
    expect(result.mayRequireJsonSchemaConfiguration).toBe(true);
    expect(result.noRequirement).toBe(false);
  });
});
