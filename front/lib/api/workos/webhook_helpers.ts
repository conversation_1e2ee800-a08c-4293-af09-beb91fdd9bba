import type { Event } from "@workos-inc/node";

import config from "@app/lib/api/config";
import { getWorkOS } from "@app/lib/api/workos/client";
import type { Result } from "@app/types";
import { Err, normalizeError, Ok } from "@app/types";

// WorkOS sends webhooks from a fixed set of IP addresses.
const workosIpAddresses = [
  "*************",
  "************",
  "**************",
  "**************",
  "*************",
  "***********",
  "***********",
  "************",
  "*************",
];

export function isWorkOSIpAddress(ipAddress: string) {
  return workosIpAddresses.includes(ipAddress);
}

export async function validateWorkOSWebhookEvent(
  payload: unknown,
  { signatureHeader }: { signatureHeader: string }
): Promise<Result<Event, Error>> {
  const workOS = getWorkOS();

  try {
    const verifiedEvent = await workOS.webhooks.constructEvent({
      payload,
      sigHeader: signatureHeader,
      secret: config.getWorkOSWebhookSigningSecret(),
    });

    return new Ok(verifiedEvent);
  } catch (error) {
    return new Err(normalizeError(error));
  }
}
