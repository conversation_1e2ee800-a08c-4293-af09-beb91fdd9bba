-- Migration created on Apr 07, 2025
CREATE TABLE IF NOT EXISTS "labs_connections_configurations" ("createdAt" TIMESTAMP WITH TIME ZONE NOT NULL, "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL, "name" VARCHAR(255) NOT NULL, "provider" VARCHAR(255) NOT NULL, "isEnabled" BOOLEAN NOT NULL DEFAULT true, "connectionId" VARCHAR(255), "credentialId" VARCHAR(255), "syncInterval" INTEGER, "syncConfig" JSON, "syncStatus" VARCHAR(255) NOT NULL DEFAULT 'idle', "lastSyncStartedAt" TIMESTAMP WITH TIME ZONE, "lastSyncCompletedAt" TIMESTAMP WITH TIME ZONE, "lastSyncError" TEXT, "lastSyncCursor" VARCHAR(255), "workspaceId" BIGINT NOT NULL REFERENCES "workspaces" ("id") ON DELETE RESTRICT ON UPDATE CASCADE, "id"  BIGSERIAL , "userId" BIGINT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE, "dataSourceViewId" BIGINT REFERENCES "data_source_views" ("id") ON DELETE SET NULL ON UPDATE CASCADE, PRIMARY KEY ("id"));
CREATE INDEX "labs_connections_configurations_user_id" ON "labs_connections_configurations" ("userId");
CREATE UNIQUE INDEX "labs_connections_configurations_user_id_workspace_id_provider" ON "labs_connections_configurations" ("userId", "workspaceId", "provider");
CREATE INDEX "labs_connections_configurations_data_source_view_id" ON "labs_connections_configurations" ("dataSourceViewId");
CREATE INDEX "labs_connections_configurations_provider" ON "labs_connections_configurations" ("provider");